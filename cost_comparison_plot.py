#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逐时段运行成本对比图生成脚本
加载最佳模型参数，与CSV文件中的真实成本进行对比

使用方法:
1. 随机选择起始时间点: python cost_comparison_plot.py
2. 手动指定起始时间点: python cost_comparison_plot.py --start-time 1000
3. 交互式选择: python cost_comparison_plot.py --interactive
4. 直接在脚本中设置: 修改下面的 MANUAL_START_TIME 变量
"""

# ==================== 手动设置参数区域 ====================
# 如果您想直接在脚本中设置起始时间点，请修改下面的变量
# 设置为 None 表示随机选择，设置为具体数字表示手动指定
MANUAL_START_TIME = None  # 例如: 1000, 5000, 10000 等 (范围: 0-17375)
# =========================================================

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import random
from matplotlib import rcParams

# 导入项目模块
from config import *
from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent
from utils.common import load_model

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

def setup_environment():
    """初始化环境"""
    # 使用与训练程序完全相同的环境配置
    from config import ENV_CONFIG
    env = IEEE30Env(**ENV_CONFIG)
    return env

def setup_agent(env):
    """初始化智能体并加载最佳模型"""
    # 初始化GNN-DDPG智能体
    agent = GNNDDPGAgent(
        node_features=env.node_features,
        edge_features=env.edge_features,
        action_dim=env.action_space.shape[0],
        action_bound=1.0,
        time_steps=1
    )
    
    # 设置环境的graph_builder给智能体
    agent.env_graph_builder = env.graph_builder
    agent.set_env(env)
    
    # 加载最佳模型
    print(f"[Debug] 智能体参数: node_features={agent.node_features}, edge_features={agent.edge_features}, hidden_dim={agent.hidden_dim}, time_steps={agent.time_steps}")

    # 检查模型文件是否存在
    model_path = os.path.join('models', 'best_gnn_model.pth')
    print(f"[Debug] 检查模型文件: {model_path}, 存在: {os.path.exists(model_path)}")

    if load_model(agent, 'models', load_best=True):
        print("[Info] 已加载最佳模型权重")
        return agent
    else:
        # 尝试加载普通模型
        print("[Warning] 无法加载最佳模型，尝试加载普通模型")
        if load_model(agent, 'models', load_best=False):
            print("[Info] 已加载普通模型权重")
            return agent
        else:
            raise FileNotFoundError("无法加载任何模型，请确保模型文件存在")

def load_csv_data():
    """加载CSV数据"""
    csv_path = 'data/power_schedule_with_wind_sequential.csv'
    if not os.path.exists(csv_path):
        raise FileNotFoundError(f"CSV文件不存在: {csv_path}")

    df = pd.read_csv(csv_path)
    print(f"[Info] 已加载CSV数据，共{len(df)}个时间点")
    return df

def run_model_prediction(agent, env, df, start_time, num_steps=96):
    """运行模型预测，获取连续96个时间步的成本"""
    model_costs = []
    
    # 设置模型为评估模式
    agent.actor.eval()
    
    with torch.no_grad():
        for step in range(num_steps):
            time_idx = start_time + step
            if time_idx >= len(df):
                break
                
            # 获取当前时刻的数据
            row = df.iloc[time_idx]
            wind_power = row['WindPower_MW']
            
            # 手动设置环境时间步
            if step == 0:
                # 第一步：设置起始时间并重置环境
                env.episode_start_step = start_time
                env.time_step = 0
                state_graph = env.reset(random_start=False)
            else:
                # 后续步骤：更新时间步和状态
                env.time_step = step

                # 更新负荷数据到当前时刻
                for i in range(len(env.ppc['bus'])):
                    env.ppc['bus'][i, 2] = env.load_data.iloc[time_idx, i]  # PD索引为2
                env.dc_pf.loads = env.ppc['bus'][:, 2].copy()

                # 更新风电数据（如果启用）
                if env.wind_power_enabled and env.wind_data is not None:
                    try:
                        env.current_wind_available = env.wind_data.iloc[time_idx]
                    except (IndexError, KeyError):
                        env.current_wind_available = 0.0

                # 获取当前状态
                state_graph = env._get_state()
            
            # 获取模型动作
            action = agent.select_action(state_graph, env.graph_builder, add_noise=False)
            
            # 执行动作并获取奖励信息
            _, _, _, info = env.step(action)

            # 获取发电成本
            gen_cost = info.get('gen_cost', 0.0)
            model_costs.append(gen_cost)

            if step % 20 == 0:  # 每20步打印一次进度
                print(f"[Progress] 已完成 {step+1}/{num_steps} 步，当前成本: ${gen_cost:.2f}")
    
    return model_costs

def create_comparison_plot(model_costs, csv_costs, start_time):
    """创建对比图"""
    time_steps = range(len(model_costs))
    
    plt.figure(figsize=(15, 8))
    
    # 绘制成本对比
    plt.plot(time_steps, model_costs, 'b-', linewidth=2, label='模型预测成本', marker='o', markersize=3)
    plt.plot(time_steps, csv_costs, 'r--', linewidth=2, label='CSV真实成本', marker='s', markersize=3)
    
    plt.xlabel('时间步 (小时)', fontsize=12)
    plt.ylabel('运行成本 (美元)', fontsize=12)
    plt.title(f'逐时段运行成本对比 (起始时刻: {start_time})', fontsize=14, fontweight='bold')
    plt.legend(fontsize=11)
    plt.grid(True, alpha=0.3)
    
    # 计算统计信息
    model_total = sum(model_costs)
    csv_total = sum(csv_costs)
    cost_ratio = model_total / csv_total if csv_total > 0 else float('inf')
    mae = np.mean(np.abs(np.array(model_costs) - np.array(csv_costs)))
    rmse = np.sqrt(np.mean((np.array(model_costs) - np.array(csv_costs))**2))
    
    # 添加统计信息文本框
    stats_text = f'统计信息:\n'
    stats_text += f'模型总成本: ${model_total:.2f}\n'
    stats_text += f'真实总成本: ${csv_total:.2f}\n'
    stats_text += f'成本比率: {cost_ratio:.4f}\n'
    stats_text += f'平均绝对误差: ${mae:.2f}\n'
    stats_text += f'均方根误差: ${rmse:.2f}'
    
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
             fontsize=10)
    
    plt.tight_layout()
    
    # 保存图片
    plot_filename = f'cost_comparison_start_{start_time}.png'
    plot_path = os.path.join('plots', plot_filename)
    os.makedirs(os.path.dirname(plot_path), exist_ok=True)
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"[Info] 对比图已保存至: {plot_path}")
    
    plt.show()
    
    return {
        'model_total_cost': model_total,
        'csv_total_cost': csv_total,
        'cost_ratio': cost_ratio,
        'mae': mae,
        'rmse': rmse
    }

def main(manual_start_time=None):
    """主函数

    Args:
        manual_start_time: 手动指定的起始时间点，如果为None则随机选择
    """
    print("=" * 60)
    print("逐时段运行成本对比分析")
    print("=" * 60)

    try:
        # 1. 加载CSV数据
        df = load_csv_data()

        # 2. 初始化环境和智能体
        env = setup_environment()
        agent = setup_agent(env)

        # 3. 选择起始时刻（手动指定或随机选择）
        max_start_time = len(df) - 96
        if max_start_time <= 0:
            raise ValueError("CSV数据不足96个时间点")

        if manual_start_time is not None:
            # 验证手动指定的起始时间点
            if manual_start_time < 0 or manual_start_time > max_start_time:
                raise ValueError(f"起始时间点必须在0到{max_start_time}之间")
            start_time = manual_start_time
            print(f"[Info] 手动指定起始时刻: {start_time}")
        else:
            start_time = random.randint(0, max_start_time)
            print(f"[Info] 随机选择起始时刻: {start_time}")
        
        # 4. 运行模型预测
        print("[Info] 开始运行模型预测...")
        model_costs = run_model_prediction(agent, env, df, start_time, num_steps=96)
        
        # 5. 获取对应的CSV真实成本
        csv_costs = df.iloc[start_time:start_time+len(model_costs)]['PeriodCost_Dollar'].tolist()
        
        # 6. 创建对比图
        print("[Info] 生成对比图...")
        stats = create_comparison_plot(model_costs, csv_costs, start_time)
        
        # 7. 输出统计结果
        print("\n" + "=" * 60)
        print("对比分析结果:")
        print("=" * 60)
        print(f"起始时刻: {start_time}")
        print(f"对比时间步数: {len(model_costs)}")
        print(f"模型总成本: ${stats['model_total_cost']:.2f}")
        print(f"真实总成本: ${stats['csv_total_cost']:.2f}")
        print(f"成本比率: {stats['cost_ratio']:.4f}")
        print(f"平均绝对误差: ${stats['mae']:.2f}")
        print(f"均方根误差: ${stats['rmse']:.2f}")
        
        if stats['cost_ratio'] < 1.0:
            print(f"✓ 模型成本比真实成本低 {(1-stats['cost_ratio'])*100:.2f}%")
        else:
            print(f"✗ 模型成本比真实成本高 {(stats['cost_ratio']-1)*100:.2f}%")
        
    except Exception as e:
        print(f"[Error] 运行过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import argparse

    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='逐时段运行成本对比分析')
    parser.add_argument('--start-time', '-s', type=int, default=None,
                        help='手动指定起始时间点 (0-17375)，如果不指定则随机选择')
    parser.add_argument('--interactive', '-i', action='store_true',
                        help='交互式选择起始时间点')

    args = parser.parse_args()

    # 优先级: 命令行参数 > 脚本内设置 > 随机选择
    if args.start_time is not None:
        manual_start_time = args.start_time
    elif MANUAL_START_TIME is not None:
        manual_start_time = MANUAL_START_TIME
        print(f"[Info] 使用脚本内设置的起始时间点: {manual_start_time}")
    else:
        manual_start_time = None

    # 处理交互式选择
    if args.interactive:
        print("=" * 60)
        print("交互式起始时间点选择")
        print("=" * 60)
        print("数据范围: 0 - 17375 (总共17472个时间点，需要连续96个时间步)")
        print("建议选择范围: 0 - 17375")

        while True:
            try:
                start_time_input = input("请输入起始时间点 (直接回车随机选择): ").strip()
                if start_time_input == "":
                    manual_start_time = None
                    break
                else:
                    manual_start_time = int(start_time_input)
                    if 0 <= manual_start_time <= 17375:
                        break
                    else:
                        print("错误: 起始时间点必须在0到17375之间")
            except ValueError:
                print("错误: 请输入有效的整数")

        main(manual_start_time)
    else:
        main(manual_start_time)
