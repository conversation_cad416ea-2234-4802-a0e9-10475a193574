"""
快速重训练脚本
使用较少的训练回合快速获得可用的模型
"""

import sys
import os

# 修改训练配置为快速训练
def quick_retrain():
    """快速重训练"""
    print("🚀 开始快速重训练...")
    print("配置: 50个回合，启用早停")
    
    # 修改配置文件
    try:
        # 读取当前配置
        with open('config.py', 'r', encoding='utf-8') as f:
            config_content = f.read()
        
        # 备份原配置
        with open('config_backup.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✅ 已备份原配置到 config_backup.py")
        
        # 修改为快速训练配置
        new_config = config_content.replace(
            "'num_episodes': 400",
            "'num_episodes': 50"
        )
        
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(new_config)
        print("✅ 已修改配置为快速训练模式 (50回合)")
        
        # 运行训练
        print("\n开始训练...")
        os.system('python train.py')
        
        # 恢复原配置
        with open('config_backup.py', 'r', encoding='utf-8') as f:
            original_config = f.read()
        
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(original_config)
        print("✅ 已恢复原配置")
        
        # 删除备份文件
        os.remove('config_backup.py')
        
        print("\n🎉 快速重训练完成!")
        print("现在可以运行成本对比测试了")
        
    except Exception as e:
        print(f"❌ 快速重训练失败: {e}")
        # 尝试恢复配置
        if os.path.exists('config_backup.py'):
            with open('config_backup.py', 'r', encoding='utf-8') as f:
                original_config = f.read()
            with open('config.py', 'w', encoding='utf-8') as f:
                f.write(original_config)
            os.remove('config_backup.py')
            print("✅ 已恢复原配置")

if __name__ == "__main__":
    quick_retrain()
